import { supabase } from "@/lib/supabase";
import { extractIdFromSlug } from "@/lib/utils/slug";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
	try {
		const slug = params.id;

		// Try to extract UUID from slug first (backward compatibility)
		let serviceId = extractIdFromSlug(slug);
		let query;

		if (serviceId) {
			// If we found a UUID in the slug, query by ID
			query = supabase
				.from("services")
				.select(
					`
	        *,
	        pricing_tiers (
	          id,
	          tier_name,
	          price,
	          min_age,
	          max_age,
	          is_active
	        ),
	        service_equipment_requirements (
	          capacity_per_participant,
	          equipment (
	            id,
	            name,
	            total_capacity
	          )
	        )
	      `
				)
				.eq("id", serviceId)
				.eq("is_active", true);
		} else {
			// If no UUID found, treat as name slug and query by name
			// Convert slug back to a searchable format
			const serviceName = slug
				.replace(/-/g, " ")
				.split(" ")
				.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
				.join(" ");

			query = supabase
				.from("services")
				.select(
					`
	        *,
	        pricing_tiers (
	          id,
	          tier_name,
	          price,
	          min_age,
	          max_age,
	          is_active
	        ),
	        service_equipment_requirements (
	          capacity_per_participant,
	          equipment (
	            id,
	            name,
	            total_capacity
	          )
	        )
	      `
				)
				.ilike("name", `%${serviceName}%`)
				.eq("is_active", true);
		}

		const { data: service, error } = await query.single();

		if (error) {
			console.error("Error fetching service:", error);
			return NextResponse.json({ error: "Service not found" }, { status: 404 });
		}

		if (!service) {
			return NextResponse.json({ error: "Service not found" }, { status: 404 });
		}

		// Transform the data to match the expected format
		const transformedService = {
			id: service.id,
			name: service.name,
			description: service.description,
			duration_minutes: service.duration_minutes,
			base_price: service.base_price,
			max_participants: service.max_participants,
			min_age: service.min_age,
			max_age: service.max_age,
			is_family_friendly: service.is_family_friendly,
			is_active: service.is_active,
			image_url: service.image_url,
			buffer_time_minutes: service.buffer_time_minutes,

			// Computed fields for UI compatibility
			duration: `${Math.floor(service.duration_minutes / 60)}h${
				service.duration_minutes % 60 > 0 ? ` ${service.duration_minutes % 60}min` : ""
			}`,
			capacity: `${service.max_participants} personnes max`,
			ageLimit: service.max_age
				? `${service.min_age}-${service.max_age} ans`
				: `À partir de ${service.min_age} ans`,

			// Pricing tiers for age-based pricing
			pricing_tiers: (service.pricing_tiers || [])
				.filter((tier: any) => tier.is_active)
				.sort((a: any, b: any) => a.min_age - b.min_age),

			// Equipment requirements for capacity calculation
			equipment_requirements: service.service_equipment_requirements || [],

			// Database fields
			category: service.category || "Service",
			location: service.location || "Petit-Canal",
			features: service.features || [],
			schedule: service.schedule || [],
			gallery: service.gallery || (service.image_url ? [service.image_url] : []),
		};

		return NextResponse.json({
			service: transformedService,
		});
	} catch (error) {
		console.error("Unexpected error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}
