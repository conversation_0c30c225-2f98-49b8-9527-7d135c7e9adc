"use client";

import { clients as initialClients } from "@/data/adminMockData";
import { Client } from "@/types/admin";
import {
	Calendar,
	Download,
	Edit,
	Eye,
	Filter,
	Mail,
	MapPin,
	Phone,
	Plus,
	Search,
	Star,
	Trash2,
	TrendingUp,
	Users,
} from "lucide-react";
import { useState } from "react";
import Button from "./ui/Button";

const AdminClients = () => {
	const [clients, setClients] = useState<Client[]>(initialClients);
	const [selectedClient, setSelectedClient] = useState<Client | null>(null);
	const [filterStatus, setFilterStatus] = useState<string>("all");
	const [searchTerm, setSearchTerm] = useState("");
	const [sortBy, setSortBy] = useState<"name" | "totalSpent" | "lastVisit" | "totalBookings">("name");

	// Helper functions
	const getClientType = (totalSpent: number) => {
		if (totalSpent >= 1000) return "VIP";
		if (totalSpent >= 500) return "Premium";
		return "Standard";
	};

	const getClientTypeColor = (totalSpent: number) => {
		if (totalSpent >= 1000) return "bg-purple-100 text-purple-800";
		if (totalSpent >= 500) return "bg-blue-100 text-blue-800";
		return "bg-gray-100 text-gray-800";
	};

	const getStatusColor = (status: string) => {
		return status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800";
	};

	const getStatusText = (status: string) => {
		return status === "active" ? "Actif" : "Inactif";
	};

	const handleViewDetails = (client: Client) => {
		setSelectedClient(client);
	};

	// Filter and sort clients
	const filteredClients = clients
		.filter((client) => {
			const matchesSearch =
				client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				client.email.toLowerCase().includes(searchTerm.toLowerCase());
			const matchesStatus = filterStatus === "all" || client.status === filterStatus;
			return matchesSearch && matchesStatus;
		})
		.sort((a, b) => {
			switch (sortBy) {
				case "name":
					return a.name.localeCompare(b.name);
				case "totalSpent":
					return b.totalSpent - a.totalSpent;
				case "lastVisit":
					return new Date(b.lastVisit).getTime() - new Date(a.lastVisit).getTime();
				case "totalBookings":
					return b.totalBookings - a.totalBookings;
				default:
					return 0;
			}
		});

	// Calculate statistics
	const totalClients = clients.length;
	const activeClients = clients.filter((c) => c.status === "active").length;
	const vipClients = clients.filter((c) => c.totalSpent >= 1000).length;
	const averageSpent = clients.reduce((sum, c) => sum + c.totalSpent, 0) / totalClients;
	const totalRevenue = clients.reduce((sum, c) => sum + c.totalSpent, 0);

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Clients</h1>
					<p className="text-gray-600">Suivez et gérez votre base de clients</p>
				</div>
				<Button icon={Plus}>Nouveau Client</Button>
			</div>

			{/* Statistics Cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm font-medium text-gray-600">Total Clients</p>
							<p className="text-2xl font-bold text-gray-900">{totalClients}</p>
						</div>
						<div className="bg-blue-100 p-3 rounded-lg">
							<Users className="h-6 w-6 text-blue-600" />
						</div>
					</div>
				</div>

				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm font-medium text-gray-600">Clients Actifs</p>
							<p className="text-2xl font-bold text-gray-900">{activeClients}</p>
						</div>
						<div className="bg-green-100 p-3 rounded-lg">
							<TrendingUp className="h-6 w-6 text-green-600" />
						</div>
					</div>
				</div>

				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm font-medium text-gray-600">Clients VIP</p>
							<p className="text-2xl font-bold text-gray-900">{vipClients}</p>
						</div>
						<div className="bg-purple-100 p-3 rounded-lg">
							<Star className="h-6 w-6 text-purple-600" />
						</div>
					</div>
				</div>

				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm font-medium text-gray-600">Dépense Moyenne</p>
							<p className="text-2xl font-bold text-gray-900">€{Math.round(averageSpent)}</p>
						</div>
						<div className="bg-yellow-100 p-3 rounded-lg">
							<TrendingUp className="h-6 w-6 text-yellow-600" />
						</div>
					</div>
				</div>

				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm font-medium text-gray-600">Chiffre d'Affaires</p>
							<p className="text-2xl font-bold text-gray-900">€{totalRevenue}</p>
						</div>
						<div className="bg-emerald-100 p-3 rounded-lg">
							<TrendingUp className="h-6 w-6 text-emerald-600" />
						</div>
					</div>
				</div>
			</div>

			{/* Filters and Controls */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
				<div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
					<div className="flex flex-col sm:flex-row gap-4 flex-1">
						<div className="relative flex-1">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
							<input
								type="text"
								placeholder="Rechercher par nom ou email..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>
						<select
							value={filterStatus}
							onChange={(e) => setFilterStatus(e.target.value)}
							className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						>
							<option value="all">Tous les statuts</option>
							<option value="active">Actif</option>
							<option value="inactive">Inactif</option>
						</select>
						<select
							value={sortBy}
							onChange={(e) => setSortBy(e.target.value as any)}
							className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						>
							<option value="name">Trier par nom</option>
							<option value="totalSpent">Trier par dépenses</option>
							<option value="lastVisit">Trier par dernière visite</option>
							<option value="totalBookings">Trier par réservations</option>
						</select>
					</div>
					<div className="flex gap-2">
						<Button variant="outline" icon={Download}>
							Exporter
						</Button>
						<Button variant="outline" icon={Filter}>
							Filtres avancés
						</Button>
					</div>
				</div>
			</div>

			{/* Clients Table */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
				<div className="overflow-x-auto">
					<table className="w-full">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Client
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Contact
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Type
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Réservations
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Total Dépensé
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Dernière Visite
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Statut
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredClients.map((client) => (
								<tr key={client.id} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center">
											<div className="h-10 w-10 bg-emerald-100 rounded-full flex items-center justify-center">
												<span className="text-emerald-600 font-medium">
													{client.name
														.split(" ")
														.map((n) => n[0])
														.join("")
														.toUpperCase()}
												</span>
											</div>
											<div className="ml-4">
												<div className="text-sm font-medium text-gray-900">{client.name}</div>
												<div className="text-sm text-gray-500 flex items-center">
													<MapPin className="h-3 w-3 mr-1" />
													{client.location}
												</div>
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="text-sm text-gray-900">{client.email}</div>
										<div className="text-sm text-gray-500">{client.phone}</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<span
											className={`px-2 py-1 text-xs font-medium rounded-full ${getClientTypeColor(
												client.totalSpent
											)}`}
										>
											{getClientType(client.totalSpent)}
										</span>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
										{client.totalBookings}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
										€{client.totalSpent}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
										{new Date(client.lastVisit).toLocaleDateString("fr-FR")}
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<span
											className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
												client.status
											)}`}
										>
											{getStatusText(client.status)}
										</span>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
										<div className="flex gap-2">
											<button
												onClick={() => handleViewDetails(client)}
												className="text-emerald-600 hover:text-emerald-900"
												title="Voir détails"
											>
												<Eye className="h-4 w-4" />
											</button>
											<button
												onClick={() => window.open(`mailto:${client.email}`)}
												className="text-blue-600 hover:text-blue-900"
												title="Envoyer un email"
											>
												<Mail className="h-4 w-4" />
											</button>
											<button
												onClick={() => console.log("Edit client:", client.id)}
												className="text-gray-600 hover:text-gray-900"
												title="Modifier"
											>
												<Edit className="h-4 w-4" />
											</button>
											<button
												onClick={() => console.log("Delete client:", client.id)}
												className="text-red-600 hover:text-red-900"
												title="Supprimer"
											>
												<Trash2 className="h-4 w-4" />
											</button>
										</div>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>

			{/* Empty State */}
			{filteredClients.length === 0 && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
					<Users className="mx-auto h-12 w-12 text-gray-400" />
					<h3 className="mt-2 text-sm font-medium text-gray-900">Aucun client trouvé</h3>
					<p className="mt-1 text-sm text-gray-500">
						Aucun client ne correspond à vos critères de recherche.
					</p>
				</div>
			)}

			{/* Client Details Modal */}
			{selectedClient && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
						<div className="p-6 border-b border-gray-200">
							<div className="flex justify-between items-start">
								<div className="flex items-center gap-4">
									<div className="h-16 w-16 bg-emerald-100 rounded-full flex items-center justify-center">
										<span className="text-emerald-600 font-bold text-xl">
											{selectedClient.name
												.split(" ")
												.map((n) => n[0])
												.join("")
												.toUpperCase()}
										</span>
									</div>
									<div>
										<h2 className="text-2xl font-bold text-gray-900">{selectedClient.name}</h2>
										<span
											className={`px-2 py-1 text-xs font-medium rounded-full ${getClientTypeColor(
												selectedClient.totalSpent
											)}`}
										>
											{getClientType(selectedClient.totalSpent)}
										</span>
									</div>
								</div>
								<button
									onClick={() => setSelectedClient(null)}
									className="text-gray-400 hover:text-gray-600"
								>
									<svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M6 18L18 6M6 6l12 12"
										/>
									</svg>
								</button>
							</div>
						</div>

						<div className="p-6">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
								<div>
									<h3 className="text-lg font-semibold text-gray-900 mb-4">
										Informations de contact
									</h3>
									<div className="space-y-3">
										<div className="flex items-center gap-3">
											<Mail className="h-5 w-5 text-gray-400" />
											<span className="text-gray-900">{selectedClient.email}</span>
										</div>
										<div className="flex items-center gap-3">
											<Phone className="h-5 w-5 text-gray-400" />
											<span className="text-gray-900">{selectedClient.phone}</span>
										</div>
										<div className="flex items-center gap-3">
											<MapPin className="h-5 w-5 text-gray-400" />
											<span className="text-gray-900">{selectedClient.location}</span>
										</div>
									</div>
								</div>

								<div>
									<h3 className="text-lg font-semibold text-gray-900 mb-4">Statistiques</h3>
									<div className="grid grid-cols-2 gap-4">
										<div className="bg-gray-50 p-4 rounded-lg text-center">
											<div className="text-2xl font-bold text-gray-900">
												{selectedClient.totalBookings}
											</div>
											<div className="text-sm text-gray-600">Réservations</div>
										</div>
										<div className="bg-gray-50 p-4 rounded-lg text-center">
											<div className="text-2xl font-bold text-emerald-600">
												€{selectedClient.totalSpent}
											</div>
											<div className="text-sm text-gray-600">Total dépensé</div>
										</div>
									</div>
								</div>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
								<div>
									<h3 className="text-lg font-semibold text-gray-900 mb-4">Historique</h3>
									<div className="space-y-3">
										<div className="flex justify-between">
											<span className="text-gray-600">Première visite:</span>
											<span className="text-gray-900">
												{new Date(selectedClient.firstVisit).toLocaleDateString("fr-FR")}
											</span>
										</div>
										<div className="flex justify-between">
											<span className="text-gray-600">Dernière visite:</span>
											<span className="text-gray-900">
												{new Date(selectedClient.lastVisit).toLocaleDateString("fr-FR")}
											</span>
										</div>
										<div className="flex justify-between">
											<span className="text-gray-600">Statut:</span>
											<span
												className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
													selectedClient.status
												)}`}
											>
												{getStatusText(selectedClient.status)}
											</span>
										</div>
									</div>
								</div>

								{selectedClient.favoriteServices.length > 0 && (
									<div>
										<h3 className="text-lg font-semibold text-gray-900 mb-4">Services favoris</h3>
										<div className="space-y-2">
											{selectedClient.favoriteServices.map((service, index) => (
												<div
													key={index}
													className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
												>
													<span className="text-gray-900">{service.name}</span>
													<span className="text-sm text-gray-600">
														{service.bookings} réservations
													</span>
												</div>
											))}
										</div>
									</div>
								)}
							</div>

							<div className="flex gap-3 pt-4 border-t border-gray-200">
								<Button icon={Mail}>Envoyer un email</Button>
								<Button variant="outline" icon={Phone}>
									Appeler
								</Button>
								<Button variant="outline" icon={Calendar}>
									Nouvelle réservation
								</Button>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default AdminClients;
