"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { Clock, Heart, MapPin, Star, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface ServiceCardProps {
	service: {
		id: string;
		name: string;
		description: string | null;
		duration: string;
		capacity: string;
		ageLimit: string;
		rating: number;
		reviews: number;
		category: string;
		difficulty: string;
		location: string;
		features: string[];
		image_url: string | null;
		base_price: number;
	};
	index: number;
	onDetailsClick: (service: any) => void;
}

export function ServiceCard({ service, index, onDetailsClick }: ServiceCardProps) {
	const serviceSlug = createServiceSlug(service.name, service.id);

	return (
		<motion.div
			initial={{ opacity: 0, y: 50 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.6, delay: index * 0.1 }}
			whileHover={{ y: -10 }}
			className="group"
		>
			<Card className="overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 border-0 bg-white">
				<div className="relative overflow-hidden">
					<Image
						src={service.image_url || "/images/waterbikes_service.png"}
						alt={service.name}
						width={400}
						height={300}
						className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
					/>
					<div className="absolute top-4 left-4">
						<Badge className="bg-white/90 text-emerald-700 font-semibold">{service.category}</Badge>
					</div>
					<div className="absolute top-4 right-4">
						<Button
							size="sm"
							variant="ghost"
							className="bg-white/90 hover:bg-white text-gray-700 rounded-full p-2"
						>
							<Heart className="w-4 h-4" />
						</Button>
					</div>
					<div className="absolute bottom-4 left-4">
						<Badge variant="outline" className="bg-white/90 text-emerald-700 border-emerald-200">
							{service.difficulty}
						</Badge>
					</div>
				</div>

				<CardContent className="p-6">
					<h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-emerald-600 transition-colors">
						{service.name}
					</h3>
					<p className="text-gray-600 mb-4 text-sm leading-relaxed">{service.description}</p>

					<div className="flex items-center gap-4 mb-4 text-sm text-gray-500">
						<div className="flex items-center">
							<Clock className="w-4 h-4 mr-1" />
							{service.duration}
						</div>
						<div className="flex items-center">
							<Users className="w-4 h-4 mr-1" />
							{service.capacity}
						</div>
						<div className="flex items-center">
							<MapPin className="w-4 h-4 mr-1" />
							<span className="truncate">{service.location.split(",")[0]}</span>
						</div>
					</div>

					<div className="flex items-center justify-between mb-4">
						<div className="flex items-center text-sm text-gray-500">
							<Star className="w-4 h-4 text-yellow-400 fill-current mr-1" />
							{service.rating.toFixed(1)} ({service.reviews} avis)
						</div>
						<div className="text-sm font-medium text-emerald-600">{service.ageLimit}</div>
					</div>

					<div className="flex items-center justify-between mb-4">
						<div className="text-lg font-bold text-emerald-600">À partir de {service.base_price}€</div>
					</div>

					<div className="flex gap-2">
						<Link href={`/services/${serviceSlug}`} className="flex-1">
							<Button
								variant="outline"
								className="w-full border-emerald-200 text-emerald-600 hover:bg-emerald-50"
							>
								Détails
							</Button>
						</Link>
						<Link href={`/services/${serviceSlug}`} className="flex-1">
							<Button className="w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white">
								Réserver
							</Button>
						</Link>
					</div>
				</CardContent>
			</Card>
		</motion.div>
	);
}
