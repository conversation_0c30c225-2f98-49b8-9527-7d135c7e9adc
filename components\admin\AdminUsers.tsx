"use client";

import { adminUsers as initialUsers } from "@/data/adminMockData";
import { AdminUser } from "@/types/admin";
import { Edit, Mail, Plus, Search, Shield, Trash2, User } from "lucide-react";
import { useState } from "react";
import Button from "./ui/Button";

const AdminUsers = () => {
	const [users, setUsers] = useState<AdminUser[]>(initialUsers);
	const [isCreating, setIsCreating] = useState(false);
	const [isEditing, setIsEditing] = useState<string | null>(null);
	const [searchTerm, setSearchTerm] = useState("");
	const [userForm, setUserForm] = useState<Partial<AdminUser>>({});

	const filteredUsers = users.filter(
		(user) =>
			user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
			user.role.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const handleCreate = () => {
		setIsCreating(true);
		setUserForm({
			name: "",
			email: "",
			role: "employee",
			permissions: [],
		});
	};

	const handleEdit = (user: AdminUser) => {
		setIsEditing(user.id);
		setUserForm(user);
	};

	const handleSave = () => {
		if (isCreating) {
			const newUser: AdminUser = {
				...(userForm as AdminUser),
				id: Date.now().toString(),
			};
			setUsers([...users, newUser]);
			setIsCreating(false);
			console.log("User created:", newUser);
		} else if (isEditing) {
			setUsers(users.map((u) => (u.id === isEditing ? { ...u, ...userForm } : u)));
			setIsEditing(null);
			console.log("User updated:", userForm);
		}
		setUserForm({});
	};

	const handleCancel = () => {
		setIsCreating(false);
		setIsEditing(null);
		setUserForm({});
	};

	const handleDelete = (userId: string) => {
		if (window.confirm("Êtes-vous sûr de vouloir supprimer cet utilisateur ?")) {
			setUsers(users.filter((u) => u.id !== userId));
			console.log("User deleted:", userId);
		}
	};

	const handleInvite = () => {
		alert("Invitation envoyée avec succès !");
	};

	const getRoleIcon = (role: string) => {
		switch (role) {
			case "admin":
				return <Shield className="h-5 w-5 text-red-600" />;
			case "manager":
				return <UserCheck className="h-5 w-5 text-blue-600" />;
			default:
				return <User className="h-5 w-5 text-gray-600" />;
		}
	};

	const getRoleColor = (role: string) => {
		switch (role) {
			case "admin":
				return "bg-red-100 text-red-800";
			case "manager":
				return "bg-blue-100 text-blue-800";
			case "employee":
				return "bg-green-100 text-green-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const getRoleText = (role: string) => {
		switch (role) {
			case "admin":
				return "Administrateur";
			case "manager":
				return "Manager";
			case "employee":
				return "Employé";
			default:
				return role;
		}
	};

	const getPermissionsList = (role: string) => {
		switch (role) {
			case "admin":
				return ["Tous les droits"];
			case "manager":
				return ["Réservations", "Services", "Employés"];
			case "employee":
				return ["Consultation uniquement"];
			default:
				return [];
		}
	};

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Utilisateurs</h1>
					<p className="text-gray-600">Gérez les accès et permissions de votre équipe</p>
				</div>
				<div className="flex gap-4">
					<Button variant="outline" onClick={handleInvite} icon={Mail}>
						Inviter un utilisateur
					</Button>
					<Button onClick={handleCreate} icon={Plus}>
						Nouvel Utilisateur
					</Button>
				</div>
			</div>

			{/* Search */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
				<div className="relative">
					<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
					<input
						type="text"
						placeholder="Rechercher un utilisateur..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
					/>
				</div>
			</div>

			{/* Create/Edit Form */}
			{(isCreating || isEditing) && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
					<h2 className="text-xl font-bold text-gray-900 mb-6">
						{isCreating ? "Créer un nouvel utilisateur" : "Modifier l'utilisateur"}
					</h2>

					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<div className="space-y-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Nom complet *</label>
								<input
									type="text"
									value={userForm.name || ""}
									onChange={(e) => setUserForm((prev) => ({ ...prev, name: e.target.value }))}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Email *</label>
								<input
									type="email"
									value={userForm.email || ""}
									onChange={(e) => setUserForm((prev) => ({ ...prev, email: e.target.value }))}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Rôle *</label>
								<select
									value={userForm.role || "employee"}
									onChange={(e) => setUserForm((prev) => ({ ...prev, role: e.target.value as any }))}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								>
									<option value="employee">Employé</option>
									<option value="manager">Manager</option>
									<option value="admin">Administrateur</option>
								</select>
							</div>
						</div>

						<div>
							<h3 className="text-lg font-semibold text-gray-900 mb-4">Permissions</h3>
							<div className="space-y-3 p-4 bg-gray-50 rounded-lg">
								{getPermissionsList(userForm.role || "employee").map((permission, index) => (
									<div key={index} className="flex items-center gap-2">
										<Shield className="h-4 w-4 text-emerald-600" />
										<span className="text-sm text-gray-700">{permission}</span>
									</div>
								))}
							</div>

							<div className="mt-4 p-4 bg-blue-50 rounded-lg">
								<h4 className="font-medium text-blue-900 mb-2">Description du rôle</h4>
								<p className="text-sm text-blue-800">
									{userForm.role === "admin" &&
										"Accès complet à toutes les fonctionnalités de l'administration."}
									{userForm.role === "manager" &&
										"Peut gérer les réservations, services et employés."}
									{userForm.role === "employee" && "Accès en lecture seule aux informations de base."}
								</p>
							</div>
						</div>
					</div>

					<div className="flex justify-end gap-4 mt-6 pt-6 border-t">
						<Button variant="outline" onClick={handleCancel}>
							Annuler
						</Button>
						<Button onClick={handleSave}>{isCreating ? "Créer l'utilisateur" : "Sauvegarder"}</Button>
					</div>
				</div>
			)}
			{/* Users List */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200">
				<div className="p-6 border-b border-gray-200">
					<h2 className="text-xl font-bold text-gray-900">Utilisateurs ({filteredUsers.length})</h2>
				</div>

				<div className="divide-y divide-gray-200">
					{filteredUsers.map((user) => (
						<div key={user.id} className="p-6">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-4">
									<div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
										<User className="h-6 w-6 text-gray-600" />
									</div>
									<div>
										<h3 className="text-lg font-semibold text-gray-900">{user.name}</h3>
										<p className="text-gray-600">{user.email}</p>
										<div className="flex items-center gap-2 mt-1">
											<span
												className={`px-2 py-1 text-xs font-medium rounded-full ${getRoleColor(
													user.role
												)}`}
											>
												{getRoleText(user.role)}
											</span>
											<span className="text-xs text-gray-500">
												{getPermissionsList(user.role).join(", ")}
											</span>
										</div>
									</div>
								</div>

								<div className="flex gap-2">
									<Button variant="outline" size="sm" onClick={() => handleEdit(user)} icon={Edit}>
										Modifier
									</Button>
									{user.role !== "admin" && (
										<Button
											variant="outline"
											size="sm"
											onClick={() => handleDelete(user.id)}
											icon={Trash2}
											className="text-red-600 border-red-200 hover:bg-red-50"
										>
											Supprimer
										</Button>
									)}
								</div>
							</div>
						</div>
					))}
				</div>

				{filteredUsers.length === 0 && (
					<div className="text-center py-12">
						<User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<p className="text-gray-500 text-lg">Aucun utilisateur trouvé</p>
					</div>
				)}
			</div>

			{/* Role Permissions Info */}
			<div className="mt-8 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Permissions par rôle</h3>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					<div className="p-4 bg-red-50 rounded-lg">
						<div className="flex items-center gap-2 mb-2">
							<Shield className="h-5 w-5 text-red-600" />
							<span className="font-medium text-red-900">Administrateur</span>
						</div>
						<ul className="text-sm text-red-800 space-y-1">
							<li>• Accès complet</li>
							<li>• Gestion des utilisateurs</li>
							<li>• Configuration système</li>
							<li>• Tous les rapports</li>
						</ul>
					</div>

					<div className="p-4 bg-blue-50 rounded-lg">
						<div className="flex items-center gap-2 mb-2">
							<Shield className="h-5 w-5 text-blue-600" />
							<span className="font-medium text-blue-900">Manager</span>
						</div>
						<ul className="text-sm text-blue-800 space-y-1">
							<li>• Gestion des réservations</li>
							<li>• Gestion des services</li>
							<li>• Gestion des employés</li>
							<li>• Rapports opérationnels</li>
						</ul>
					</div>

					<div className="p-4 bg-green-50 rounded-lg">
						<div className="flex items-center gap-2 mb-2">
							<Shield className="h-5 w-5 text-green-600" />
							<span className="font-medium text-green-900">Employé</span>
						</div>
						<ul className="text-sm text-green-800 space-y-1">
							<li>• Consultation des réservations</li>
							<li>• Consultation du planning</li>
							<li>• Mise à jour du profil</li>
							<li>• Rapports personnels</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	);
};

export default AdminUsers;
