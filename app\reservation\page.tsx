"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import {
  CalendarIcon,
  Clock,
  Users,
  ChevronRight,
  ChevronLeft,
  Plus,
  Minus,
  X,
  Check,
  CreditCard,
  AlertCircle,
  Loader2,
  Euro,
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"

import { useSearchParams } from "next/navigation"
import { Calendar } from "@/components/ui/calendar" // Import du composant Calendar

// Types
interface Service {
  id: number
  title: string
  duration: string
  price: number
  capacity: number
  image: string
  description: string
  ageLimit: string
  features: string[]
}

interface TimeSlot {
  time: string
  available: boolean
  spotsLeft: number
}

interface BookingItem {
  service: Service
  date: string
  timeSlot: string
  participants: number
  totalPrice: number
}

interface FormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  cardNumber: string
  expiryDate: string
  cvv: string
  specialRequests: string
}

// Mock data
const services: Service[] = [
  {
    id: 1,
    title: "Excursion en WaterBikes",
    duration: "2h",
    price: 25,
    capacity: 8,
    image: "/images/waterbikes_service.png",
    description: "Explorez le littoral de manière ludique et éco-responsable",
    ageLimit: "À partir de 12 ans",
    features: ["Collation offerte", "Sac étanche fourni", "Guide expérimenté"],
  },
  {
    id: 2,
    title: "Visite Guidée Culturelle",
    duration: "3h",
    price: 30,
    capacity: 12,
    image: "/images/cultural_tour_service.png",
    description: "Plongez au cœur de l'histoire guadeloupéenne",
    ageLimit: "Gratuit pour les -12 ans",
    features: ["Collation offerte", "Chasse au trésor ludique", "Guide passionné"],
  },
  {
    id: 3,
    title: "Rencontre avec les Pélicans",
    duration: "3h",
    price: 45,
    capacity: 8,
    image: "/images/pelican_encounter_service.png",
    description: "Aventure unique au cœur d'un îlet sauvage",
    ageLimit: "Gratuit pour les -6 ans",
    features: ["Collation offerte", "Jumelles d'observation", "Îlet sauvage"],
  },
  {
    id: 4,
    title: "Dégustation de Produits Locaux",
    duration: "1.5h",
    price: 35,
    capacity: 15,
    image: "/images/local_products_service.png",
    description: "Expérience gustative avec des saveurs authentiques",
    ageLimit: "Tous âges",
    features: ["Produits du terroir", "Saveurs authentiques", "Guide gastronome"],
  },
  {
    id: 5,
    title: "Exploration de la Mangrove",
    duration: "2.5h",
    price: 40,
    capacity: 6,
    image: "/images/mangrove_exploration_service.png",
    description: "Naviguez à travers la mangrove préservée",
    ageLimit: "À partir de 8 ans",
    features: ["Approche éco-responsable", "Guide écologue", "Biodiversité"],
  },
  {
    id: 6,
    title: "Aventure Famille au Coucher du Soleil",
    duration: "2h",
    price: 50,
    capacity: 10,
    image: "/images/sunset_family_adventure_service.png",
    description: "Moment magique en famille au coucher du soleil",
    ageLimit: "Tous âges",
    features: ["Moment privilégié", "Observation faune", "Photos souvenirs"],
  },
]

// Generate mock availability data
const generateTimeSlots = (serviceId: number, date: string): TimeSlot[] => {
  const baseSlots = [
    { time: "08:00", available: true, spotsLeft: 8 },
    { time: "10:30", available: true, spotsLeft: 5 },
    { time: "13:00", available: true, spotsLeft: 12 },
    { time: "15:30", available: true, spotsLeft: 3 },
    { time: "17:30", available: true, spotsLeft: 10 },
  ]

  // Simulate some unavailable slots
  return baseSlots.map((slot) => ({
    ...slot,
    available: Math.random() > 0.2, // 80% chance of being available
    spotsLeft: Math.floor(Math.random() * 12) + 1,
  }))
}

const steps = [
  { id: 1, title: "Services", description: "Choisissez vos excursions" },
  { id: 2, title: "Date & Heure", description: "Sélectionnez vos créneaux" },
  { id: 3, title: "Participants", description: "Nombre de personnes" },
  { id: 4, title: "Informations", description: "Vos coordonnées" },
  { id: 5, title: "Paiement", description: "Finaliser la réservation" },
]

export default function ReservationPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedServices, setSelectedServices] = useState<number[]>([])
  const [bookingItems, setBookingItems] = useState<BookingItem[]>([])
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined) // Changer le type pour Date | undefined
  const [timeSlots, setTimeSlots] = useState<{ [key: number]: TimeSlot[] }>({})
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState<FormData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    cardNumber: "",
    expiryDate: "",
    cvv: "",
    specialRequests: "",
  })
  const [formErrors, setFormErrors] = useState<Partial<FormData>>({})

  const searchParams = useSearchParams()
  const preSelectedServiceId = searchParams.get("service")

  useEffect(() => {
    if (preSelectedServiceId) {
      const serviceId = Number.parseInt(preSelectedServiceId)
      if (services.find((s) => s.id === serviceId)) {
        setSelectedServices([serviceId])
        setCurrentStep(2)
      }
    }
  }, [preSelectedServiceId])

  // Supprimer la fonction generateDates car le composant Calendar gère la plage

  // Load time slots when date changes
  useEffect(() => {
    if (selectedDate && selectedServices.length > 0) {
      const slots: { [key: number]: TimeSlot[] } = {}
      selectedServices.forEach((serviceId) => {
        slots[serviceId] = generateTimeSlots(serviceId, selectedDate.toISOString().split("T")[0])
      })
      setTimeSlots(slots)
    }
  }, [selectedDate, selectedServices])

  const toggleService = (serviceId: number) => {
    setSelectedServices((prev) =>
      prev.includes(serviceId) ? prev.filter((id) => id !== serviceId) : [...prev, serviceId],
    )
  }

  const addBookingItem = (serviceId: number, timeSlot: string) => {
    const service = services.find((s) => s.id === serviceId)
    if (!service || !selectedDate) return

    const newItem: BookingItem = {
      service,
      date: selectedDate.toISOString().split("T")[0], // Convertir Date en string pour BookingItem
      timeSlot,
      participants: 1,
      totalPrice: service.price,
    }

    setBookingItems((prev) => [...prev, newItem])
  }

  const removeBookingItem = (index: number) => {
    setBookingItems((prev) => prev.filter((_, i) => i !== index))
  }

  const updateParticipants = (index: number, participants: number) => {
    setBookingItems((prev) =>
      prev.map((item, i) =>
        i === index ? { ...item, participants, totalPrice: item.service.price * participants } : item,
      ),
    )
  }

  const getTotalPrice = () => {
    return bookingItems.reduce((total, item) => total + item.totalPrice, 0)
  }

  const validateForm = (): boolean => {
    const errors: Partial<FormData> = {}

    if (!formData.firstName.trim()) errors.firstName = "Prénom requis"
    if (!formData.lastName.trim()) errors.lastName = "Nom requis"
    if (!formData.email.trim()) errors.email = "Email requis"
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) errors.email = "Email invalide"
    if (!formData.phone.trim()) errors.phone = "Téléphone requis"
    if (!formData.cardNumber.trim()) errors.cardNumber = "Numéro de carte requis"
    else if (!/^\d{16}$/.test(formData.cardNumber.replace(/\s/g, ""))) errors.cardNumber = "16 chiffres requis"
    if (!formData.expiryDate.trim()) errors.expiryDate = "Date d'expiration requise"
    if (!formData.cvv.trim()) errors.cvv = "CVV requis"
    else if (!/^\d{3}$/.test(formData.cvv)) errors.cvv = "3 chiffres requis"

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    setIsLoading(true)

    // Simulate payment processing
    await new Promise((resolve) => setTimeout(resolve, 3000))

    // Simulate success/failure (90% success rate)
    const success = Math.random() > 0.1

    if (success) {
      // Redirect to confirmation page
      window.location.href = `/reservation/confirmation?booking=${Date.now()}`
    } else {
      setIsLoading(false)
      alert("Erreur de paiement. Veuillez réessayer.")
    }
  }

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 1:
        return selectedServices.length > 0
      case 2:
        return bookingItems.length > 0
      case 3:
        return bookingItems.every((item) => item.participants > 0)
      case 4:
        return formData.firstName && formData.lastName && formData.email && formData.phone
      default:
        return true
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("fr-FR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "")
    const matches = v.match(/\d{4,16}/g)
    const match = (matches && matches[0]) || ""
    const parts = []
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4))
    }
    if (parts.length) {
      return parts.join(" ")
    } else {
      return v
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50">
      {/* Header */}
      <header className="bg-white/90 backdrop-blur-md shadow-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/images/logo-hd.png"
                alt="Soleil & Découverte"
                width={60}
                height={60}
                className="object-contain"
              />
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-yellow-500 bg-clip-text text-transparent">
                  Soleil & Découverte
                </h1>
                <p className="text-sm text-emerald-600">Réservation en ligne</p>
              </div>
            </Link>

            <Link href="/">
              <Button variant="ghost" size="sm">
                <X className="w-4 h-4 mr-2" />
                Fermer
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Progress Steps */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-semibold ${
                    currentStep >= step.id ? "bg-emerald-500 text-white" : "bg-gray-200 text-gray-500"
                  }`}
                >
                  {currentStep > step.id ? <Check className="w-4 h-4" /> : step.id}
                </div>
                <div className="ml-2 hidden sm:block">
                  <div
                    className={`text-sm font-medium ${currentStep >= step.id ? "text-emerald-600" : "text-gray-500"}`}
                  >
                    {step.title}
                  </div>
                  <div className="text-xs text-gray-400">{step.description}</div>
                </div>
                {index < steps.length - 1 && <ChevronRight className="w-4 h-4 text-gray-300 mx-2 hidden sm:block" />}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            {/* Step 1: Service Selection */}
            {currentStep === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">Choisissez vos excursions</h2>
                  <p className="text-gray-600">Sélectionnez une ou plusieurs activités pour votre aventure</p>
                </div>

                <div className="grid md:grid-cols-2 gap-6 mb-8">
                  {services.map((service) => (
                    <motion.div key={service.id} whileHover={{ y: -5 }} whileTap={{ scale: 0.98 }}>
                      <Card
                        className={`cursor-pointer transition-all duration-300 ${
                          selectedServices.includes(service.id)
                            ? "ring-2 ring-emerald-500 bg-emerald-50"
                            : "hover:shadow-lg"
                        }`}
                        onClick={() => toggleService(service.id)}
                      >
                        <div className="relative">
                          <Image
                            src={service.image || "/placeholder.svg"}
                            alt={service.title}
                            width={400}
                            height={200}
                            className="w-full h-48 object-cover rounded-t-lg"
                          />
                          {selectedServices.includes(service.id) && (
                            <div className="absolute top-4 right-4 bg-emerald-500 text-white rounded-full p-2">
                              <Check className="w-4 h-4" />
                            </div>
                          )}
                          {preSelectedServiceId && Number.parseInt(preSelectedServiceId) === service.id && (
                            <div className="absolute top-4 left-4 bg-orange-500 text-white rounded-full px-3 py-1 text-sm font-semibold">
                              Recommandé
                            </div>
                          )}
                          <div className="absolute bottom-4 left-4">
                            <Badge className="bg-white/90 text-emerald-700 font-semibold">{service.ageLimit}</Badge>
                          </div>
                        </div>

                        <CardContent className="p-6">
                          <h3 className="text-xl font-bold text-gray-900 mb-2">{service.title}</h3>
                          <p className="text-gray-600 mb-4 text-sm">{service.description}</p>

                          <div className="flex items-center gap-4 mb-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <Clock className="w-4 h-4 mr-1" />
                              {service.duration}
                            </div>
                            <div className="flex items-center">
                              <Users className="w-4 h-4 mr-1" />
                              Max {service.capacity}
                            </div>
                            <div className="flex items-center">
                              <Euro className="w-4 h-4 mr-1" />
                              {service.price}€
                            </div>
                          </div>

                          <div className="space-y-1">
                            {service.features.slice(0, 2).map((feature, idx) => (
                              <div key={idx} className="flex items-center text-sm text-emerald-600">
                                <div className="w-2 h-2 bg-emerald-400 rounded-full mr-2"></div>
                                {feature}
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Step 2: Date & Time Selection */}
            {currentStep === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">Choisissez votre date</h2>
                  <p className="text-gray-600">Sélectionnez une date puis vos créneaux horaires</p>
                </div>

                {/* Date Selection */}
                <Card className="mb-8">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CalendarIcon className="w-5 h-5" />
                      Sélectionnez une date
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex justify-center">
                    <Calendar
                      mode="single"
                      selected={selectedDate}
                      onSelect={setSelectedDate}
                      initialFocus
                      fromDate={new Date()}
                      toDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))} // Un an à partir d'aujourd'hui
                      disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))} // Désactiver les dates passées
                      className="rounded-md border"
                    />
                  </CardContent>
                </Card>

                {/* Time Slot Selection */}
                {selectedDate && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="space-y-6">
                      {selectedServices.map((serviceId) => {
                        const service = services.find((s) => s.id === serviceId)
                        const slots = timeSlots[serviceId] || []

                        return (
                          <Card key={serviceId}>
                            <CardHeader>
                              <CardTitle className="flex items-center gap-2">
                                <Clock className="w-5 h-5" />
                                {service?.title}
                              </CardTitle>
                              <p className="text-sm text-gray-600">
                                {formatDate(selectedDate.toISOString().split("T")[0])} à Choisissez un créneau
                              </p>
                            </CardHeader>
                            <CardContent>
                              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3">
                                {slots.map((slot) => (
                                  <Button
                                    key={slot.time}
                                    variant="outline"
                                    disabled={!slot.available}
                                    className={`h-auto p-3 flex flex-col ${
                                      !slot.available
                                        ? "opacity-50 cursor-not-allowed"
                                        : "hover:bg-emerald-50 hover:border-emerald-300"
                                    }`}
                                    onClick={() => slot.available && addBookingItem(serviceId, slot.time)}
                                  >
                                    <div className="text-lg font-bold">{slot.time}</div>
                                    <div className="text-xs text-gray-500">
                                      {slot.available ? `${slot.spotsLeft} places` : "Complet"}
                                    </div>
                                  </Button>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        )
                      })}
                    </div>
                  </motion.div>
                )}

                {/* Selected Bookings */}
                {bookingItems.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="mt-8"
                  >
                    <Card>
                      <CardHeader>
                        <CardTitle>Vos réservations sélectionnées</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {bookingItems.map((item, index) => (
                            <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                              <div>
                                <h4 className="font-semibold">{item.service.title}</h4>
                                <p className="text-sm text-gray-600">
                                  {formatDate(item.date)} à {item.timeSlot}
                                </p>
                              </div>
                              <Button variant="ghost" size="sm" onClick={() => removeBookingItem(index)}>
                                <X className="w-4 h-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )}
              </motion.div>
            )}

            {/* Step 3: Participants */}
            {currentStep === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">Nombre de participants</h2>
                  <p className="text-gray-600">Indiquez le nombre de personnes pour chaque excursion</p>
                </div>

                <div className="space-y-6">
                  {bookingItems.map((item, index) => (
                    <Card key={index}>
                      <CardContent className="p-6">
                        <div className="flex items-center gap-4 mb-4">
                          <Image
                            src={item.service.image || "/placeholder.svg"}
                            alt={item.service.title}
                            width={80}
                            height={80}
                            className="rounded-lg object-cover"
                          />
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold">{item.service.title}</h3>
                            <p className="text-sm text-gray-600">
                              {formatDate(item.date)} à {item.timeSlot}
                            </p>
                            <p className="text-sm text-emerald-600">{item.service.ageLimit}</p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <Label className="text-sm font-medium">Participants:</Label>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => updateParticipants(index, Math.max(1, item.participants - 1))}
                                disabled={item.participants <= 1}
                              >
                                <Minus className="w-4 h-4" />
                              </Button>
                              <span className="w-12 text-center font-semibold">{item.participants}</span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  updateParticipants(index, Math.min(item.service.capacity, item.participants + 1))
                                }
                                disabled={item.participants >= item.service.capacity}
                              >
                                <Plus className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-gray-500">
                              {item.service.price}€ × {item.participants}
                            </div>
                            <div className="text-lg font-bold text-emerald-600">{item.totalPrice}€</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  <Card className="bg-emerald-50 border-emerald-200">
                    <CardContent className="p-6">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-semibold">Total</span>
                        <span className="text-2xl font-bold text-emerald-600">{getTotalPrice()}€</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </motion.div>
            )}

            {/* Step 4: Personal Information */}
            {currentStep === 4 && (
              <motion.div
                key="step4"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">Vos informations</h2>
                  <p className="text-gray-600">Renseignez vos coordonnées pour la réservation</p>
                </div>

                <Card>
                  <CardContent className="p-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="firstName">Prénom *</Label>
                        <Input
                          id="firstName"
                          value={formData.firstName}
                          onChange={(e) => setFormData((prev) => ({ ...prev, firstName: e.target.value }))}
                          className={formErrors.firstName ? "border-red-500" : ""}
                        />
                        {formErrors.firstName && <p className="text-red-500 text-sm mt-1">{formErrors.firstName}</p>}
                      </div>

                      <div>
                        <Label htmlFor="lastName">Nom *</Label>
                        <Input
                          id="lastName"
                          value={formData.lastName}
                          onChange={(e) => setFormData((prev) => ({ ...prev, lastName: e.target.value }))}
                          className={formErrors.lastName ? "border-red-500" : ""}
                        />
                        {formErrors.lastName && <p className="text-red-500 text-sm mt-1">{formErrors.lastName}</p>}
                      </div>

                      <div>
                        <Label htmlFor="email">Email *</Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                          className={formErrors.email ? "border-red-500" : ""}
                        />
                        {formErrors.email && <p className="text-red-500 text-sm mt-1">{formErrors.email}</p>}
                      </div>

                      <div>
                        <Label htmlFor="phone">Téléphone *</Label>
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => setFormData((prev) => ({ ...prev, phone: e.target.value }))}
                          className={formErrors.phone ? "border-red-500" : ""}
                          placeholder="+33 6 XX XX XX XX"
                        />
                        {formErrors.phone && <p className="text-red-500 text-sm mt-1">{formErrors.phone}</p>}
                      </div>

                      <div className="md:col-span-2">
                        <Label htmlFor="specialRequests">Demandes spéciales (optionnel)</Label>
                        <Textarea
                          id="specialRequests"
                          value={formData.specialRequests}
                          onChange={(e) => setFormData((prev) => ({ ...prev, specialRequests: e.target.value }))}
                          placeholder="Allergies, besoins particuliers, etc."
                          rows={3}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Step 5: Payment */}
            {currentStep === 5 && (
              <motion.div
                key="step5"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">Paiement sécurisé</h2>
                  <p className="text-gray-600">Finalisez votre réservation</p>
                </div>

                <div className="grid lg:grid-cols-2 gap-8">
                  {/* Payment Form */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <CreditCard className="w-5 h-5" />
                        Informations de paiement
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="cardNumber">Numéro de carte *</Label>
                          <Input
                            id="cardNumber"
                            value={formData.cardNumber}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                cardNumber: formatCardNumber(e.target.value),
                              }))
                            }
                            className={formErrors.cardNumber ? "border-red-500" : ""}
                            placeholder="1234 5678 9012 3456"
                            maxLength={19}
                          />
                          {formErrors.cardNumber && (
                            <p className="text-red-500 text-sm mt-1">{formErrors.cardNumber}</p>
                          )}
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="expiryDate">Date d'expiration *</Label>
                            <Input
                              id="expiryDate"
                              value={formData.expiryDate}
                              onChange={(e) => setFormData((prev) => ({ ...prev, expiryDate: e.target.value }))}
                              className={formErrors.expiryDate ? "border-red-500" : ""}
                              placeholder="MM/AA"
                              maxLength={5}
                            />
                            {formErrors.expiryDate && (
                              <p className="text-red-500 text-sm mt-1">{formErrors.expiryDate}</p>
                            )}
                          </div>

                          <div>
                            <Label htmlFor="cvv">CVV *</Label>
                            <Input
                              id="cvv"
                              value={formData.cvv}
                              onChange={(e) => setFormData((prev) => ({ ...prev, cvv: e.target.value }))}
                              className={formErrors.cvv ? "border-red-500" : ""}
                              placeholder="123"
                              maxLength={3}
                            />
                            {formErrors.cvv && <p className="text-red-500 text-sm mt-1">{formErrors.cvv}</p>}
                          </div>
                        </div>

                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <AlertCircle className="w-4 h-4" />
                          Paiement sécurisé SSL 256 bits
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Order Summary */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Récapitulatif de commande</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {bookingItems.map((item, index) => (
                          <div key={index} className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="font-medium">{item.service.title}</h4>
                              <p className="text-sm text-gray-600">
                                {formatDate(item.date)} à {item.timeSlot}
                              </p>
                              <p className="text-sm text-gray-600">
                                {item.participants} participant{item.participants > 1 ? "s" : ""}
                              </p>
                            </div>
                            <div className="text-right">
                              <span className="font-semibold">{item.totalPrice}€</span>
                            </div>
                          </div>
                        ))}

                        <Separator />

                        <div className="flex justify-between items-center text-lg font-bold">
                          <span>Total</span>
                          <span className="text-emerald-600">{getTotalPrice()}€</span>
                        </div>

                        <div className="text-sm text-gray-600">
                          <p>• Confirmation immédiate par email</p>
                          <p>• Annulation gratuite jusqu'à 24h avant</p>
                          <p>• Support client 7j/7</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Navigation Buttons */}
          <div className="flex justify-between items-center mt-8 pt-8 border-t">
            <Button
              variant="outline"
              onClick={() => setCurrentStep((prev) => Math.max(1, prev - 1))}
              disabled={currentStep === 1}
              className="flex items-center gap-2"
            >
              <ChevronLeft className="w-4 h-4" />
              Précédent
            </Button>

            {currentStep < 5 ? (
              <Button
                onClick={() => setCurrentStep((prev) => prev + 1)}
                disabled={!canProceedToNextStep()}
                className="flex items-center gap-2 bg-emerald-500 hover:bg-emerald-600"
              >
                Suivant
                <ChevronRight className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={isLoading || !canProceedToNextStep()}
                className="flex items-center gap-2 bg-emerald-500 hover:bg-emerald-600"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Traitement...
                  </>
                ) : (
                  <>
                    Finaliser la réservation
                    <CreditCard className="w-4 h-4" />
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
