"use client";

import { reservations as mockReservations } from "@/data/adminMockData";
import { Reservation } from "@/types/admin";
import { Calendar, Eye, Mail, Phone, Search, Trash2 } from "lucide-react";
import { useState } from "react";
import Button from "./ui/Button";

const AdminReservations = () => {
	const [reservations, setReservations] = useState<Reservation[]>(mockReservations);
	const [selectedReservation, setSelectedReservation] = useState<Reservation | null>(null);
	const [filterStatus, setFilterStatus] = useState<string>("all");
	const [searchTerm, setSearchTerm] = useState("");

	const filteredReservations = reservations.filter((reservation) => {
		const matchesStatus = filterStatus === "all" || reservation.status === filterStatus;
		const matchesSearch =
			searchTerm === "" ||
			reservation.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
			reservation.serviceName.toLowerCase().includes(searchTerm.toLowerCase());

		return matchesStatus && matchesSearch;
	});

	const handleViewDetails = (reservation: Reservation) => {
		setSelectedReservation(reservation);
	};

	const handleStatusChange = (reservationId: string, newStatus: "confirmed" | "pending" | "cancelled") => {
		setReservations(reservations.map((r) => (r.id === reservationId ? { ...r, status: newStatus } : r)));
		console.log("Reservation status updated:", reservationId, newStatus);
	};

	const handleDelete = (reservationId: string) => {
		if (window.confirm("Êtes-vous sûr de vouloir supprimer cette réservation ?")) {
			setReservations(reservations.filter((r) => r.id !== reservationId));
			console.log("Reservation deleted:", reservationId);
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "confirmed":
				return "bg-green-100 text-green-800";
			case "pending":
				return "bg-yellow-100 text-yellow-800";
			case "cancelled":
				return "bg-red-100 text-red-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const getStatusText = (status: string) => {
		switch (status) {
			case "confirmed":
				return "Confirmé";
			case "pending":
				return "En attente";
			case "cancelled":
				return "Annulé";
			default:
				return status;
		}
	};

	return (
		<div className="p-6">
			<div className="mb-8">
				<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Réservations</h1>
				<p className="text-gray-600">Gérez toutes les réservations de vos clients</p>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
				<div className="flex flex-col lg:flex-row gap-4">
					<div className="relative flex-1">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
						<input
							type="text"
							placeholder="Rechercher par nom de client ou service..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						/>
					</div>

					<div className="flex gap-2">
						<select
							value={filterStatus}
							onChange={(e) => setFilterStatus(e.target.value)}
							className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						>
							<option value="all">Tous les statuts</option>
							<option value="confirmed">Confirmé</option>
							<option value="pending">En attente</option>
							<option value="cancelled">Annulé</option>
						</select>
					</div>
				</div>
			</div>

			{/* Reservations Table */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
				<div className="p-6 border-b border-gray-200">
					<h2 className="text-xl font-bold text-gray-900">Réservations ({filteredReservations.length})</h2>
				</div>

				<div className="overflow-x-auto">
					<table className="w-full">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Client
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Service
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Date & Heure
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Participants
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Montant
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Statut
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredReservations.map((reservation) => (
								<tr key={reservation.id} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap">
										<div>
											<div className="text-sm font-medium text-gray-900">
												{reservation.customerName}
											</div>
											<div className="text-sm text-gray-500">{reservation.customerEmail}</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="text-sm text-gray-900">{reservation.serviceName}</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="text-sm text-gray-900">
											{new Date(reservation.date).toLocaleDateString("fr-FR")}
										</div>
										<div className="text-sm text-gray-500">{reservation.time}</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
										{reservation.participants}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
										€{reservation.totalPrice}
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<select
											value={reservation.status}
											onChange={(e) => handleStatusChange(reservation.id, e.target.value as any)}
											className={`px-2 py-1 text-xs font-medium rounded-full border-0 ${getStatusColor(
												reservation.status
											)}`}
										>
											<option value="pending">En attente</option>
											<option value="confirmed">Confirmé</option>
											<option value="cancelled">Annulé</option>
										</select>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
										<div className="flex gap-2">
											<button
												onClick={() => handleViewDetails(reservation)}
												className="text-emerald-600 hover:text-emerald-900"
											>
												<Eye className="h-4 w-4" />
											</button>
											<button
												onClick={() => handleDelete(reservation.id)}
												className="text-red-600 hover:text-red-900"
											>
												<Trash2 className="h-4 w-4" />
											</button>
										</div>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>

				{filteredReservations.length === 0 && (
					<div className="text-center py-12">
						<Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<p className="text-gray-500 text-lg">Aucune réservation trouvée</p>
					</div>
				)}
			</div>

			{/* Reservation Details Modal */}
			{selectedReservation && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
						<div className="p-6 border-b border-gray-200">
							<div className="flex justify-between items-center">
								<h2 className="text-2xl font-bold text-gray-900">Détails de la réservation</h2>
								<button
									onClick={() => setSelectedReservation(null)}
									className="text-gray-400 hover:text-gray-600"
								>
									✕
								</button>
							</div>
						</div>

						<div className="p-6 space-y-6">
							{/* Customer Information */}
							<div>
								<h3 className="text-lg font-semibold text-gray-900 mb-3">Informations client</h3>
								<div className="bg-gray-50 rounded-lg p-4 space-y-2">
									<div className="flex justify-between">
										<span className="text-gray-600">Nom:</span>
										<span className="font-medium">{selectedReservation.customerName}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Email:</span>
										<span className="font-medium">{selectedReservation.customerEmail}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Téléphone:</span>
										<span className="font-medium">{selectedReservation.customerPhone}</span>
									</div>
								</div>
							</div>

							{/* Reservation Details */}
							<div>
								<h3 className="text-lg font-semibold text-gray-900 mb-3">Détails de la réservation</h3>
								<div className="bg-gray-50 rounded-lg p-4 space-y-2">
									<div className="flex justify-between">
										<span className="text-gray-600">Service:</span>
										<span className="font-medium">{selectedReservation.serviceName}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Date:</span>
										<span className="font-medium">
											{new Date(selectedReservation.date).toLocaleDateString("fr-FR")}
										</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Heure:</span>
										<span className="font-medium">{selectedReservation.time}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Participants:</span>
										<span className="font-medium">{selectedReservation.participants}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Prix total:</span>
										<span className="font-medium text-emerald-600">
											€{selectedReservation.totalPrice}
										</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Statut:</span>
										<span
											className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
												selectedReservation.status
											)}`}
										>
											{getStatusText(selectedReservation.status)}
										</span>
									</div>
								</div>
							</div>

							{/* Actions */}
							<div className="flex flex-col sm:flex-row gap-3">
								<Button
									onClick={() => window.open(`mailto:${selectedReservation.customerEmail}`)}
									className="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700"
								>
									<Mail className="h-4 w-4" />
									Envoyer un email
								</Button>
								<Button
									onClick={() => window.open(`tel:${selectedReservation.customerPhone}`)}
									className="flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700"
								>
									<Phone className="h-4 w-4" />
									Appeler
								</Button>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default AdminReservations;
