"use client";

import React from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { Bell, User, LogOut } from 'lucide-react';

const AdminHeader = () => {
  const pathname = usePathname();
  const router = useRouter();

  const handleLogout = () => {
    console.log('Logout clicked');
    localStorage.removeItem('admin_token');
    router.push('/admin/login');
  };

  if (pathname === '/admin/login') return null;

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link href="/admin/dashboard" className="flex items-center space-x-3">
            <img 
              src="/images/logo-hd.png" 
              alt="Soleil & Découverte" 
              className="h-10 w-auto"
            />
            <span className="text-lg font-semibold text-gray-900">Administration</span>
          </Link>

          <div className="flex items-center space-x-4">
            <Link href="/admin/notifications" className="p-2 text-gray-400 hover:text-gray-600 relative">
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full flex items-center justify-center text-xs text-white">
                3
              </span>
            </Link>
            
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-emerald-100 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-emerald-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">Admin</span>
            </div>

            <button
              onClick={handleLogout}
              className="p-2 text-gray-400 hover:text-gray-600"
              title="Se déconnecter"
            >
              <LogOut className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default AdminHeader;
