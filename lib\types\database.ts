export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          first_name: string | null
          last_name: string | null
          phone: string | null
          role: 'admin' | 'employee' | 'customer'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          first_name?: string | null
          last_name?: string | null
          phone?: string | null
          role?: 'admin' | 'employee' | 'customer'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          first_name?: string | null
          last_name?: string | null
          phone?: string | null
          role?: 'admin' | 'employee' | 'customer'
          created_at?: string
          updated_at?: string
        }
      }
      services: {
        Row: {
          id: string
          name: string
          description: string | null
          duration_minutes: number
          buffer_time_minutes: number
          base_price: number
          max_participants: number
          min_age: number
          max_age: number | null
          is_family_friendly: boolean
          is_active: boolean
          image_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          duration_minutes: number
          buffer_time_minutes?: number
          base_price: number
          max_participants: number
          min_age?: number
          max_age?: number | null
          is_family_friendly?: boolean
          is_active?: boolean
          image_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          duration_minutes?: number
          buffer_time_minutes?: number
          base_price?: number
          max_participants?: number
          min_age?: number
          max_age?: number | null
          is_family_friendly?: boolean
          is_active?: boolean
          image_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      equipment: {
        Row: {
          id: string
          name: string
          description: string | null
          total_capacity: number
          capacity_per_participant: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          total_capacity?: number
          capacity_per_participant?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          total_capacity?: number
          capacity_per_participant?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      pricing_tiers: {
        Row: {
          id: string
          service_id: string
          tier_name: string
          min_age: number
          max_age: number | null
          price: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          service_id: string
          tier_name: string
          min_age?: number
          max_age?: number | null
          price: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          service_id?: string
          tier_name?: string
          min_age?: number
          max_age?: number | null
          price?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      time_slots: {
        Row: {
          id: string
          service_id: string
          start_time: string
          end_time: string
          assigned_employee_id: string | null
          status: 'available' | 'booked' | 'cancelled' | 'completed'
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          service_id: string
          start_time: string
          end_time: string
          assigned_employee_id?: string | null
          status?: 'available' | 'booked' | 'cancelled' | 'completed'
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          service_id?: string
          start_time?: string
          end_time?: string
          assigned_employee_id?: string | null
          status?: 'available' | 'booked' | 'cancelled' | 'completed'
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      reservations: {
        Row: {
          id: string
          customer_id: string
          time_slot_id: string
          reservation_number: string
          participant_count: number
          total_amount: number
          currency: string
          status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'no_show'
          special_requests: string | null
          discount_code: string | null
          discount_amount: number
          check_in_time: string | null
          qr_code: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          time_slot_id: string
          reservation_number: string
          participant_count?: number
          total_amount: number
          currency?: string
          status?: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'no_show'
          special_requests?: string | null
          discount_code?: string | null
          discount_amount?: number
          check_in_time?: string | null
          qr_code?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          time_slot_id?: string
          reservation_number?: string
          participant_count?: number
          total_amount?: number
          currency?: string
          status?: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'no_show'
          special_requests?: string | null
          discount_code?: string | null
          discount_amount?: number
          check_in_time?: string | null
          qr_code?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
