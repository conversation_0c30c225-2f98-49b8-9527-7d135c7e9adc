"use client";

import AdminHeader from "./AdminHeader";
import AdminSidebar from "./AdminSidebar";

interface AdminLayoutProps {
	children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
	return (
		<div className="flex">
			<AdminSidebar />
			<div className="flex-1">
				<AdminHeader />
				<main className="p-6">
					{children}
				</main>
			</div>
		</div>
	);
}
