# 🗄️ Database Setup - Soleil et Découverte

## ✅ Supabase Project Created

**Project Details:**
- **Project ID**: `zalzjvuxoffmhaokvzda`
- **Project Name**: `soleil-et-decouverte`
- **Region**: `eu-west-1`
- **Database Host**: `db.zalzjvuxoffmhaokvzda.supabase.co`
- **Status**: `ACTIVE_HEALTHY`

## 📊 Database Schema

### **Core Tables Created:**
1. **`profiles`** - User authentication and roles (extends auth.users)
2. **`services`** - Eco-tourism services with buffer time management
3. **`equipment`** - Equipment/resources with capacity management
4. **`service_equipment_requirements`** - Links services to required equipment
5. **`pricing_tiers`** - Age-based pricing (adult, child, senior, etc.)
6. **`employees`** - Staff management with skills and languages
7. **`customers`** - Customer profiles with emergency contacts
8. **`time_slots`** - Available service time slots
9. **`equipment_reservations`** - Equipment capacity reservations
10. **`reservations`** - Main booking system with QR codes
11. **`reservation_participants`** - Group booking participants
12. **`payments`** - Payment transactions (Stripe/PayPal)
13. **`refunds`** - Refund processing system
14. **`discount_coupons`** - Promotional codes and discounts
15. **`customer_feedback`** - Reviews and ratings system
16. **`notifications`** - Email notification tracking

### **Key Features:**
- ✅ **Equipment-Service Integration**: Capacity automatically managed
- ✅ **Age-based Pricing**: Different rates for different age groups
- ✅ **Buffer Time Management**: Service-level buffer between bookings
- ✅ **Row Level Security**: Multi-tenant access control
- ✅ **Performance Indexes**: 24 indexes for optimized queries
- ✅ **Automatic Timestamps**: Updated_at triggers on all tables
- ✅ **Generic Naming**: Template-ready for other businesses

## 🔧 Environment Configuration

### **Files Created:**
- **`.env.local`** - Development environment (with actual keys)
- **`.env.example`** - Template for production setup

### **Key Environment Variables:**
```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://zalzjvuxoffmhaokvzda.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Database
DATABASE_URL=************************************************************************************/postgres
```

## 🔐 Security Setup

### **Row Level Security Policies:**
- ✅ **Admin Access**: Full access to all data
- ✅ **Employee Access**: Operational data access
- ✅ **Customer Access**: Own data only
- ✅ **Public Access**: Services and pricing for booking interface

### **Authentication:**
- ✅ **Supabase Auth**: Built-in user management
- ✅ **Role-based Access**: admin, employee, customer roles
- ✅ **Profile Extension**: Additional business-specific user data

## 📈 Performance Optimization

### **Indexes Created (24 total):**
- Profile lookups (role, email)
- Service filtering (active, family-friendly)
- Reservation queries (customer, status, date)
- Payment tracking (status, date)
- Time slot availability (service, time, status)
- Equipment reservations (equipment, time slot)
- Feedback analysis (rating, customer)

### **Triggers:**
- ✅ **Auto-timestamps**: 13 tables with automatic updated_at
- ✅ **Data integrity**: Constraints and foreign keys

## 🚀 Next Steps

1. **Install Supabase Client**: `npm install @supabase/supabase-js`
2. **Create API Routes**: Server-side business logic
3. **Connect Frontend**: Update React components
4. **Add Sample Data**: Populate tables for testing
5. **Payment Integration**: Stripe/PayPal setup
6. **Email Service**: Notification system

## 📝 Database Access

### **Supabase Dashboard:**
- URL: https://supabase.com/dashboard/project/zalzjvuxoffmhaokvzda
- Direct database access via dashboard SQL editor

### **Connection Details:**
- **Host**: db.zalzjvuxoffmhaokvzda.supabase.co
- **Port**: 5432
- **Database**: postgres
- **Username**: postgres
- **Password**: SoleilDecouverte2024!

## ⚠️ Important Notes

1. **Service Role Key**: Keep private, server-side only
2. **Anon Key**: Safe for frontend use
3. **Database Password**: Store securely, don't commit to git
4. **RLS Policies**: Will need refinement based on business rules
5. **Business Logic**: Implement in API routes, not database functions

The database is now ready to support all essential/priority features for the eco-tourism booking system!
