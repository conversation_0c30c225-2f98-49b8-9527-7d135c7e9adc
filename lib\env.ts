// Environment variables validation and configuration

function getEnvVar(name: string, defaultValue?: string): string {
  const value = process.env[name] || defaultValue
  
  if (!value) {
    throw new Error(`Missing required environment variable: ${name}`)
  }
  
  return value
}

function getOptionalEnvVar(name: string, defaultValue?: string): string | undefined {
  return process.env[name] || defaultValue
}

// Supabase configuration
export const supabaseConfig = {
  url: getEnvVar('NEXT_PUBLIC_SUPABASE_URL'),
  anonKey: getEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
  serviceRoleKey: getEnvVar('SUPABASE_SERVICE_ROLE_KEY'),
} as const

// Application configuration
export const appConfig = {
  url: getEnvVar('NEXT_PUBLIC_APP_URL', 'http://localhost:3000'),
  nodeEnv: getEnvVar('NODE_ENV', 'development'),
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
} as const

// Business configuration
export const businessConfig = {
  name: getOptionalEnvVar('BUSINESS_NAME', 'Soleil et Découverte'),
  email: getOptionalEnvVar('BUSINESS_EMAIL', '<EMAIL>'),
  phone: getOptionalEnvVar('BUSINESS_PHONE', '+33 6 40 24 44 25'),
  address: getOptionalEnvVar('BUSINESS_ADDRESS', 'Petit-Canal, Guadeloupe'),
  defaultCurrency: getOptionalEnvVar('DEFAULT_CURRENCY', 'EUR'),
  defaultBufferTime: parseInt(getOptionalEnvVar('DEFAULT_BUFFER_TIME_MINUTES', '15')),
  maxAdvanceBookingDays: parseInt(getOptionalEnvVar('MAX_ADVANCE_BOOKING_DAYS', '365')),
  cancellationDeadlineHours: parseInt(getOptionalEnvVar('CANCELLATION_DEADLINE_HOURS', '24')),
} as const

// Payment configuration (will be used later)
export const paymentConfig = {
  stripe: {
    publishableKey: getOptionalEnvVar('STRIPE_PUBLISHABLE_KEY'),
    secretKey: getOptionalEnvVar('STRIPE_SECRET_KEY'),
    webhookSecret: getOptionalEnvVar('STRIPE_WEBHOOK_SECRET'),
  },
} as const

// Email configuration (will be used later)
export const emailConfig = {
  from: getOptionalEnvVar('EMAIL_FROM', '<EMAIL>'),
  apiKey: getOptionalEnvVar('EMAIL_API_KEY'),
  disableEmails: getOptionalEnvVar('DISABLE_EMAILS', 'false') === 'true',
} as const

// Debug configuration
export const debugConfig = {
  enabled: getOptionalEnvVar('DEBUG', 'false') === 'true',
  logLevel: getOptionalEnvVar('LOG_LEVEL', 'info'),
} as const

// Validate critical environment variables on startup
export function validateEnvironment() {
  try {
    // Validate Supabase configuration
    supabaseConfig.url
    supabaseConfig.anonKey
    
    // Only validate service role key on server side
    if (typeof window === 'undefined') {
      supabaseConfig.serviceRoleKey
    }
    
    console.log('✅ Environment variables validated successfully')
    
    if (debugConfig.enabled) {
      console.log('🔧 Debug mode enabled')
      console.log('📧 Email disabled:', emailConfig.disableEmails)
      console.log('🌍 Environment:', appConfig.nodeEnv)
    }
    
    return true
  } catch (error) {
    console.error('❌ Environment validation failed:', error)
    throw error
  }
}

// Call validation on module load (server-side only)
if (typeof window === 'undefined') {
  validateEnvironment()
}
