import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
	title: "Soleil et Découverte",
	description:
		"Découvrez la Guadeloupe authentique avec nos excursions éco-responsables. WaterBikes, visites culturelles et rencontres avec les pélicans.",
	keywords: "Guadeloupe, excursions, WaterBikes, écotourisme, pélicans, visites culturelles, Petit-Canal",
	authors: [{ name: "Soleil et Découverte" }],
	icons: {
		icon: "/images/logo-hd.png",
		shortcut: "/images/logo-hd.png",
		apple: "/images/logo-hd.png",
	},
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="fr">
			<body>{children}</body>
		</html>
	);
}
