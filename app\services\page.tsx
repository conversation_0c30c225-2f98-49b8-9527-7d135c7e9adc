"use client";

import { ServiceCard } from "@/components/ServiceCard";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { motion } from "framer-motion";
import { ChevronRight, Clock, Filter, Loader2, MapPin, Search, Star, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

interface Service {
	id: string;
	name: string;
	description: string | null;
	duration: string;
	capacity: string;
	ageLimit: string;
	rating: number;
	reviews: number;
	category: string;
	difficulty: string;
	location: string;
	features: string[];
	image_url: string | null;
	base_price: number;
	schedule: string[];
	gallery: string[];
}

const categories = ["Tous", "Nautique", "Culturel", "Nature", "Gastronomie", "Famille"];
const difficulties = ["Tous", "Facile", "Modéré", "Difficile"];
const durations = ["Tous", "2h", "2.5h", "3h", "4h"];

export default function ServicesPage() {
	const [services, setServices] = useState<Service[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [selectedService, setSelectedService] = useState<Service | null>(null);
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedCategory, setSelectedCategory] = useState("Tous");
	const [selectedDifficulty, setSelectedDifficulty] = useState("Tous");
	const [selectedDuration, setSelectedDuration] = useState("Tous");
	const [favorites, setFavorites] = useState<string[]>([]);

	// Fetch services from API
	useEffect(() => {
		const fetchServices = async () => {
			try {
				setLoading(true);
				const response = await fetch("/api/services");
				if (!response.ok) {
					throw new Error("Failed to fetch services");
				}
				const data = await response.json();
				setServices(data.services);
			} catch (err) {
				setError(err instanceof Error ? err.message : "An error occurred");
			} finally {
				setLoading(false);
			}
		};

		fetchServices();
	}, []);

	const filteredServices = services.filter((service) => {
		const matchesSearch =
			service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			(service.description && service.description.toLowerCase().includes(searchTerm.toLowerCase()));
		const matchesCategory = selectedCategory === "Tous" || service.category === selectedCategory;
		const matchesDifficulty = selectedDifficulty === "Tous" || service.difficulty === selectedDifficulty;
		const matchesDuration = selectedDuration === "Tous" || service.duration === selectedDuration;

		return matchesSearch && matchesCategory && matchesDifficulty && matchesDuration;
	});

	const toggleFavorite = (serviceId: string) => {
		setFavorites((prev) =>
			prev.includes(serviceId) ? prev.filter((id) => id !== serviceId) : [...prev, serviceId]
		);
	};

	return (
		<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50">
			{/* Header */}
			<header className="bg-white/90 backdrop-blur-md shadow-sm sticky top-0 z-50">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center justify-between">
						<Link href="/" className="flex items-center space-x-3">
							<Image
								src="/images/logo-hd.png"
								alt="Soleil & Découverte"
								width={60}
								height={60}
								className="object-contain"
							/>
							<div>
								<h1 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-yellow-500 bg-clip-text text-transparent">
									Soleil & Découverte
								</h1>
								<p className="text-sm text-emerald-600">L'aventure au cœur de la Guadeloupe</p>
							</div>
						</Link>

						<nav className="hidden md:flex items-center space-x-8">
							<Link
								href="/"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Accueil
							</Link>
							<Link href="/services" className="text-emerald-600 font-semibold">
								Services
							</Link>
							<Link
								href="/about"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								À propos
							</Link>
							<Link
								href="/gallery"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Galerie
							</Link>
							<Link
								href="/contact"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Contact
							</Link>
							<Link href="/reservation">
								<Button className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white px-6 py-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-300">
									Réserver
								</Button>
							</Link>
						</nav>
					</div>
				</div>
			</header>

			{/* Hero Section */}
			<section className="relative py-20 overflow-hidden">
				<Image
					src="/images/services_hero.png"
					alt="Nos Services"
					fill
					className="object-cover"
					style={{ zIndex: 1 }}
				/>
				<div className="absolute inset-0 bg-black/20" style={{ zIndex: 2 }}></div>

				<div className="container mx-auto px-4 relative z-10">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center max-w-4xl mx-auto"
					>
						<Badge className="mb-6 bg-white/20 text-white border-white/30 backdrop-blur-sm px-4 py-2 text-lg">
							🏝️ Nos Excursions
						</Badge>
						<h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
							Découvrez Tous Nos
							<span className="block bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
								Services d'Exception
							</span>
						</h1>
						<p className="text-xl text-white/90 max-w-3xl mx-auto">
							Choisissez parmi notre sélection d'excursions uniques, conçues pour vous faire vivre la
							Guadeloupe sous tous ses aspects les plus authentiques.
						</p>
					</motion.div>
				</div>
			</section>

			{/* Filters Section */}
			<section className="py-8 bg-white shadow-sm">
				<div className="container mx-auto px-4">
					<div className="flex flex-col lg:flex-row gap-4 items-center">
						<div className="flex items-center gap-2 flex-1">
							<Search className="w-5 h-5 text-gray-400" />
							<Input
								placeholder="Rechercher une excursion..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="flex-1"
							/>
						</div>

						<div className="flex flex-wrap gap-4 items-center">
							<Select value={selectedCategory} onValueChange={setSelectedCategory}>
								<SelectTrigger className="w-40">
									<SelectValue placeholder="Catégorie" />
								</SelectTrigger>
								<SelectContent>
									{categories.map((category) => (
										<SelectItem key={category} value={category}>
											{category}
										</SelectItem>
									))}
								</SelectContent>
							</Select>

							<Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
								<SelectTrigger className="w-40">
									<SelectValue placeholder="Difficulté" />
								</SelectTrigger>
								<SelectContent>
									{difficulties.map((difficulty) => (
										<SelectItem key={difficulty} value={difficulty}>
											{difficulty}
										</SelectItem>
									))}
								</SelectContent>
							</Select>

							<Select value={selectedDuration} onValueChange={setSelectedDuration}>
								<SelectTrigger className="w-32">
									<SelectValue placeholder="Durée" />
								</SelectTrigger>
								<SelectContent>
									{durations.map((duration) => (
										<SelectItem key={duration} value={duration}>
											{duration}
										</SelectItem>
									))}
								</SelectContent>
							</Select>

							<Button variant="outline" className="flex items-center gap-2">
								<Filter className="w-4 h-4" />
								Filtres
							</Button>
						</div>
					</div>
				</div>
			</section>

			{/* Services Grid */}
			<section className="py-12">
				<div className="container mx-auto px-4">
					{loading && (
						<div className="flex justify-center items-center py-20">
							<Loader2 className="w-8 h-8 animate-spin text-emerald-600" />
							<span className="ml-2 text-emerald-600">Chargement des services...</span>
						</div>
					)}

					{error && (
						<div className="text-center py-20">
							<div className="text-red-600 mb-4">
								<span className="text-lg font-semibold">Erreur de chargement</span>
							</div>
							<p className="text-gray-600 mb-4">{error}</p>
							<Button
								onClick={() => window.location.reload()}
								className="bg-emerald-600 hover:bg-emerald-700"
							>
								Réessayer
							</Button>
						</div>
					)}

					{!loading && !error && filteredServices.length === 0 && (
						<div className="text-center py-20">
							<div className="text-gray-600 mb-4">
								<span className="text-lg font-semibold">Aucun service trouvé</span>
							</div>
							<p className="text-gray-500 mb-4">Essayez de modifier vos critères de recherche.</p>
							<Button
								onClick={() => {
									setSearchTerm("");
									setSelectedCategory("Tous");
									setSelectedDifficulty("Tous");
									setSelectedDuration("Tous");
								}}
								className="bg-emerald-600 hover:bg-emerald-700"
							>
								Réinitialiser les filtres
							</Button>
						</div>
					)}

					{!loading && !error && filteredServices.length > 0 && (
						<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
							{filteredServices.map((service, index) => (
								<ServiceCard
									key={service.id}
									service={service}
									index={index}
									onDetailsClick={setSelectedService}
								/>
							))}
						</div>
					)}
				</div>
			</section>

			{/* Service Detail Modal */}
			{selectedService && (
				<div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
					<motion.div
						initial={{ opacity: 0, scale: 0.9 }}
						animate={{ opacity: 1, scale: 1 }}
						exit={{ opacity: 0, scale: 0.9 }}
						className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
					>
						<div className="relative">
							<Image
								src={selectedService.image || "/placeholder.svg"}
								alt={selectedService.title}
								width={800}
								height={400}
								className="w-full h-64 object-cover rounded-t-2xl"
							/>
							<button
								onClick={() => setSelectedService(null)}
								className="absolute top-4 right-4 bg-white/90 hover:bg-white rounded-full p-2 transition-colors"
							>
								<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M6 18L18 6M6 6l12 12"
									/>
								</svg>
							</button>
						</div>

						<div className="p-8">
							<div className="flex items-start justify-between mb-6">
								<div>
									<h2 className="text-3xl font-bold text-gray-900 mb-2">{selectedService.title}</h2>
									<div className="flex items-center gap-4 text-sm text-gray-500">
										<Badge variant="outline" className="text-emerald-600 border-emerald-200">
											{selectedService.category}
										</Badge>
										<div className="flex items-center">
											<Star className="w-4 h-4 text-yellow-400 fill-current mr-1" />
											{selectedService.rating} ({selectedService.reviews} avis)
										</div>
									</div>
								</div>
								<div className="text-right">
									<div className="text-lg font-bold text-emerald-600">{selectedService.ageLimit}</div>
									<div className="text-sm text-gray-500">Conditions d'âge</div>
								</div>
							</div>

							<p className="text-gray-700 mb-6 leading-relaxed">{selectedService.fullDescription}</p>

							<div className="grid md:grid-cols-2 gap-6 mb-6">
								<div>
									<h3 className="font-semibold text-gray-900 mb-3">Informations pratiques</h3>
									<div className="space-y-2 text-sm">
										<div className="flex items-center">
											<Clock className="w-4 h-4 mr-2 text-emerald-600" />
											Durée: {selectedService.duration}
										</div>
										<div className="flex items-center">
											<Users className="w-4 h-4 mr-2 text-emerald-600" />
											Capacité: {selectedService.capacity}
										</div>
										<div className="flex items-center">
											<MapPin className="w-4 h-4 mr-2 text-emerald-600" />
											Lieu: {selectedService.location}
										</div>
									</div>
								</div>

								<div>
									<h3 className="font-semibold text-gray-900 mb-3">Inclus dans l'excursion</h3>
									<ul className="space-y-1 text-sm">
										{selectedService.features.map((feature, idx) => (
											<li key={idx} className="flex items-center">
												<div className="w-2 h-2 bg-emerald-400 rounded-full mr-2"></div>
												{feature}
											</li>
										))}
									</ul>
								</div>
							</div>

							<div className="mb-6">
								<h3 className="font-semibold text-gray-900 mb-3">Horaires disponibles</h3>
								<div className="flex flex-wrap gap-2">
									{selectedService.schedule.map((time, idx) => (
										<Badge key={idx} variant="outline" className="px-3 py-1">
											{time}
										</Badge>
									))}
								</div>
							</div>

							<div className="flex gap-4">
								<Button variant="outline" onClick={() => setSelectedService(null)} className="flex-1">
									Fermer
								</Button>
								<Link href={`/reservation?service=${selectedService.id}`}>
									<Button className="flex-1 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white">
										Réserver Maintenant
										<ChevronRight className="w-4 h-4 ml-2" />
									</Button>
								</Link>
							</div>
						</div>
					</motion.div>
				</div>
			)}

			{/* CTA Section */}
			<section className="py-20 bg-gradient-to-br from-emerald-600 to-teal-700 relative overflow-hidden">
				<div className="absolute inset-0 bg-black/20"></div>
				<Image
					src="/images/adventure_cta.png"
					alt="Adventure"
					fill
					className="object-cover"
					style={{ zIndex: 1 }}
				/>

				<div className="container mx-auto px-4 relative z-10">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center max-w-4xl mx-auto"
					>
						<h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Besoin d'Aide pour Choisir ?</h2>
						<p className="text-xl text-white/90 mb-8">
							Notre équipe est là pour vous conseiller et créer l'excursion parfaite selon vos envies !
						</p>
						<div className="flex flex-col sm:flex-row gap-4 justify-center">
							<Link href="/contact">
								<Button
									size="lg"
									className="bg-white text-emerald-600 hover:bg-gray-100 px-8 py-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 text-lg font-semibold"
								>
									Nous Contacter
									<ChevronRight className="w-5 h-5 ml-2" />
								</Button>
							</Link>
							<Button
								size="lg"
								variant="outline"
								className="border-2 border-white text-white hover:bg-white hover:text-emerald-600 px-8 py-4 rounded-full backdrop-blur-sm bg-white/10 transition-all duration-300 text-lg font-semibold"
							>
								Excursion Sur Mesure
							</Button>
						</div>
					</motion.div>
				</div>
			</section>

			{/* Footer */}
			<footer className="bg-gray-900 text-white py-16">
				<div className="container mx-auto px-4">
					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
						<div>
							<div className="flex items-center space-x-3 mb-6">
								<Image
									src="/images/logo-hd.png"
									alt="Soleil & Découverte"
									width={50}
									height={50}
									className="object-contain"
								/>
								<div>
									<h3 className="text-xl font-bold bg-gradient-to-r from-orange-400 to-yellow-400 bg-clip-text text-transparent">
										Soleil & Découverte
									</h3>
									<p className="text-sm text-emerald-400">L'aventure au cœur de la Guadeloupe</p>
								</div>
							</div>
							<p className="text-gray-400 leading-relaxed">
								Découvrez la Guadeloupe authentique avec nos excursions uniques.
							</p>
						</div>

						<div>
							<h4 className="text-lg font-semibold mb-4 text-emerald-400">Navigation</h4>
							<ul className="space-y-2">
								<li>
									<Link href="/" className="text-gray-400 hover:text-white transition-colors">
										Accueil
									</Link>
								</li>
								<li>
									<Link href="/services" className="text-gray-400 hover:text-white transition-colors">
										Services
									</Link>
								</li>
								<li>
									<Link href="/about" className="text-gray-400 hover:text-white transition-colors">
										À propos
									</Link>
								</li>
								<li>
									<Link href="/gallery" className="text-gray-400 hover:text-white transition-colors">
										Galerie
									</Link>
								</li>
								<li>
									<Link href="/contact" className="text-gray-400 hover:text-white transition-colors">
										Contact
									</Link>
								</li>
							</ul>
						</div>

						<div>
							<h4 className="text-lg font-semibold mb-4 text-emerald-400">Services</h4>
							<ul className="space-y-2">
								<li>
									<span className="text-gray-400">Excursions WaterBikes</span>
								</li>
								<li>
									<span className="text-gray-400">Visites Culturelles</span>
								</li>
								<li>
									<span className="text-gray-400">Observation Pélicans</span>
								</li>
								<li>
									<span className="text-gray-400">Tours Personnalisés</span>
								</li>
							</ul>
						</div>

						<div>
							<h4 className="text-lg font-semibold mb-4 text-emerald-400">Contact</h4>
							<div className="space-y-3 text-gray-400">
								<div className="flex items-center">
									<MapPin className="w-4 h-4 mr-2 text-emerald-400" />
									<span className="text-sm">Petit-Canal, Guadeloupe</span>
								</div>
								<div className="flex items-center">
									<svg
										className="w-4 h-4 mr-2 text-emerald-400"
										fill="currentColor"
										viewBox="0 0 20 20"
									>
										<path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
									</svg>
									<span className="text-sm">+33 6 40 24 44 25</span>
								</div>
								<div className="flex items-center">
									<svg
										className="w-4 h-4 mr-2 text-emerald-400"
										fill="currentColor"
										viewBox="0 0 20 20"
									>
										<path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
										<path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
									</svg>
									<span className="text-sm"><EMAIL></span>
								</div>
							</div>
						</div>
					</div>

					<div className="border-t border-gray-800 mt-12 pt-8 text-center">
						<p className="text-gray-400">© 2024 Soleil & Découverte. Tous droits réservés.</p>
					</div>
				</div>
			</footer>
		</div>
	);
}
