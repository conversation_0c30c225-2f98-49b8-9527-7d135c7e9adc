import { Service, Employee, AdminUser, Reservation, Client } from '@/types/admin';

export const services: Service[] = [
  {
    id: 'mangrove-tour',
    name: 'Découverte de la Mangrove',
    description: 'Explorez les mystères de la mangrove guadeloupéenne lors de cette excursion unique. Naviguez entre les palétuviers, observez la faune locale et découvrez cet écosystème fascinant avec notre guide expérimenté.',
    shortDescription: 'Exploration guidée de la mangrove en kayak ou bateau',
    images: [
      'https://images.pexels.com/photos/1435075/pexels-photo-1435075.jpeg',
      'https://images.pexels.com/photos/1761279/pexels-photo-1761279.jpeg',
      'https://images.pexels.com/photos/1539593/pexels-photo-1539593.jpeg'
    ],
    price: 45,
    duration: 180,
    category: 'Écotourisme',
    capacity: 12,
    features: ['Guide expert', 'Équipement fourni', 'Observation de la faune', 'Photos incluses'],
    availableSlots: [
      { id: '1', date: '2025-02-01', time: '09:00', available: true, spotsLeft: 8 },
      { id: '2', date: '2025-02-01', time: '14:00', available: true, spotsLeft: 5 },
      { id: '3', date: '2025-02-02', time: '09:00', available: false, spotsLeft: 0 }
    ]
  },
  {
    id: 'catamaran-sunset',
    name: 'Coucher de Soleil en Catamaran',
    description: 'Vivez un moment magique à bord de notre catamaran tout confort. Admirez le coucher de soleil sur les eaux cristallines des Caraïbes, avec boissons et collations incluses.',
    shortDescription: 'Croisière romantique au coucher du soleil',
    images: [
      'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg',
      'https://images.pexels.com/photos/1001682/pexels-photo-1001682.jpeg',
      'https://images.pexels.com/photos/1430676/pexels-photo-1430676.jpeg'
    ],
    price: 85,
    duration: 240,
    category: 'Croisière',
    capacity: 25,
    features: ['Boissons incluses', 'Collations', 'Musique d\'ambiance', 'Photos professionnelles'],
    availableSlots: [
      { id: '4', date: '2025-02-01', time: '17:30', available: true, spotsLeft: 12 },
      { id: '5', date: '2025-02-02', time: '17:30', available: true, spotsLeft: 15 }
    ]
  },
  {
    id: 'waterbike-adventure',
    name: 'Aventure en Waterbike',
    description: 'Découvrez la Guadeloupe d\'une façon unique avec nos waterbikes nouvelle génération. Pédalez sur l\'eau en toute sécurité et explorez les côtes préservées.',
    shortDescription: 'Exploration côtière en vélo aquatique',
    images: [
      'https://images.pexels.com/photos/1761279/pexels-photo-1761279.jpeg',
      'https://images.pexels.com/photos/1435075/pexels-photo-1435075.jpeg'
    ],
    price: 35,
    duration: 120,
    category: 'Sport aquatique',
    capacity: 8,
    features: ['Équipement fourni', 'Initiation incluse', 'Encadrement professionnel'],
    availableSlots: [
      { id: '6', date: '2025-02-01', time: '10:00', available: true, spotsLeft: 6 },
      { id: '7', date: '2025-02-01', time: '15:00', available: true, spotsLeft: 3 }
    ]
  }
];

export const employees: Employee[] = [
  {
    id: 'emp-1',
    name: 'Marie Dubois',
    role: 'Guide principal',
    email: '<EMAIL>',
    avatar: '/placeholder-user.jpg',
    services: ['mangrove-tour', 'waterbike-adventure']
  },
  {
    id: 'emp-2',
    name: 'Jean-Luc Martin',
    role: 'Capitaine',
    email: '<EMAIL>',
    avatar: '/placeholder-user.jpg',
    services: ['catamaran-sunset']
  }
];

export const adminUsers: AdminUser[] = [
  {
    id: 'admin-1',
    name: 'Sophie Laroche',
    email: '<EMAIL>',
    role: 'admin',
    avatar: '/placeholder-user.jpg',
    permissions: ['all']
  }
];

export const reservations: Reservation[] = [
  {
    id: 'res-1',
    serviceId: 'mangrove-tour',
    serviceName: 'Découverte de la Mangrove',
    date: '2025-02-01',
    time: '09:00',
    participants: 4,
    customerName: 'Marie Dupont',
    customerEmail: '<EMAIL>',
    customerPhone: '+33 6 12 34 56 78',
    totalPrice: 180,
    status: 'confirmed',
    createdAt: '2025-01-15T10:30:00Z'
  }
];

export const clients: Client[] = [
  {
    id: 'client-1',
    name: 'Marie Dupont',
    email: '<EMAIL>',
    phone: '+33 6 12 34 56 78',
    location: 'Paris, France',
    status: 'active',
    totalBookings: 3,
    totalSpent: 270,
    firstVisit: '2024-08-15',
    lastVisit: '2025-01-15',
    favoriteServices: [
      { name: 'Découverte de la Mangrove', bookings: 2 }
    ],
    bookingHistory: [
      {
        id: 'res-1',
        serviceName: 'Découverte de la Mangrove',
        date: '2025-02-01',
        amount: 180,
        status: 'upcoming'
      }
    ]
  }
];
